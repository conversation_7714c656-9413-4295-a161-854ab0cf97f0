/// @ts-check

/**
 * @typedef {import('./types').TranslationKey} TranslationKey
 * @type {{ [key in TranslationKey]: string }}
 */
export const es = {
  // Global
  access: 'Acceder',
  login: 'Iniciar sesión',
  logout: '<PERSON><PERSON><PERSON> sesión',
  continue: 'Continuar',
  welcome: 'Bienvenido a {name}',
  haveAccount: '¿Tienes una cuenta?',
  petsCount: 'No tienes mascotas | Tienes una mascota | Tienes {n} mascotas',
  ageYears: 'Recién nacido | 1 año | {years} años',
  itemsCount: 'Un elemento | {count} elementos',
  flagName: 'Bandera de Perú',

  // Starter form
  identifierLabel: 'Documento de Identidad',
  identifierPlaceholder: 'Ingresa tu número de DNI',
  identifierHint: 'Este es tu identificador dentro de la plataforma',
  reasonLabel: 'Motivo de atención',
  reasonHint: 'Ayúdanos a agilizar tu atención',
  ownerData: 'Datos del propietario',
  ownerDataHint: 'Ayúdanos a agilizar tu atención',
  nameLabel: 'Nombre',
  namePlaceholder: 'Ingresa tu nombre',
  lastNameLabel: 'Apellidos',
  lastNamePlaceholder: 'Ingresa tus apellidos',
  phoneLabel: 'Número de teléfono',
  phonePlaceholder: 'Ingresa tu número de teléfono',

  // Default values
  noOptions: 'No hay opciones',
  defaultPlaceholder: 'Selecciona una opción',
  defaultError: 'Error al cargar opciones',
  defaultErrorHint: 'Intenta nuevamente en unos minutos',
}
