import { Skeleton } from '@/components/ui/skeleton'
import { useRequest } from '@/fetcher'

import { getTemporalAttention } from '../storage'

function OwnerLoader({ children }) {
  const attention = getTemporalAttention()
  const identifier = attention.identifier
  const { data } = useRequest(() => identifier && `/owners?dni=${identifier}`)
  const owner = data && data.length > 0 && data.at(0)

  if (!owner) {
    return <LoadingFallback />
  }

  return children
}

function LoadingFallback() {
  return (
    <div className="space-y-2">
      <Skeleton className="h-4 w-1/2" />
      <Skeleton className="h-9 w-full rounded-md mb-4" />

      <Skeleton className="h-4 w-1/2" />
      <Skeleton className="h-9 w-full rounded-md mb-4" />

      <Skeleton className="h-4 w-1/2" />
      <Skeleton className="h-9 w-full rounded-md mb-6" />

      <Skeleton className="h-9 w-full rounded-md" />
    </div>
  )
}

export { OwnerLoader }
