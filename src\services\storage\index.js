import { StorageKeys } from '@/constants'
import { z } from 'zod/v4'

import { BrowserStorage } from './storage'

const TemporalAttention = z.object({
  clinicId: z.coerce.number().nullable().default(null),
  identifier: z.string().length(8).regex(/^\d+$/).nullable().default(null),
  reason: z.coerce.number().nullable().default(null),
  owner: z
    .object({
      id: z.number(),
      name: z.string(),
      lastName: z.string().optional(),
      phone: z.string().length(9).regex(/^\d+$/),
    })
    .nullable()
    .default(null),
  pet: z.coerce.number().nullable().default(null),
})

const ClinicSlug = z.string().min(1).nullable()

/**
 * Instance of BrowserStorage with registered keys.
 *
 * @type {BrowserStorage & {
 *   attention: object,
 *   clinicSlug: string | null,
 * }}
 */
const storage = new BrowserStorage()

storage.registerKey('attention', StorageKeys.attention, TemporalAttention)
storage.registerKey('clinicSlug', StorageKeys.clinicSlug, ClinicSlug)

export { storage }
