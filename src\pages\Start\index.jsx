import { useRequest } from '@/fetcher'
import { useTranslation } from '@/i18n'
import { useAuth0 } from '@auth0/auth0-react'
import { useEffect, useState } from 'react'
import { useSearchParams } from 'react-router-dom'

import { OwnerForm } from './components/owner-form'
import { OwnerLoader } from './components/owner-loader'
import { StarterCard } from './components/starter-card'
import { StarterForm } from './components/starter-form'
import { addTo, getTemporalAttention } from './storage'

const QueryParams = {
  CLINIC: 'q',
}

export default function Start() {
  const { t } = useTranslation()
  const { loginWithRedirect } = useAuth0()
  const [searchParams] = useSearchParams()
  const [attention, setAttention] = useState(getTemporalAttention)

  const slug = searchParams.get(QueryParams.CLINIC)
  const { data: clinic } = useRequest(() => slug && `/clinics/${slug}`)

  const clinicName = clinic && clinic.name
  const clinicId = clinic && clinic.id

  useEffect(() => addTo(attention), [attention])

  if (!attention.identifier || !attention.reason) {
    return (
      <StarterCard
        title={clinicName}
        description={t('welcome', { name: clinicName })}
        onAccess={loginWithRedirect}
      >
        <StarterForm clinicId={clinicId} onPayload={setAttention} />
      </StarterCard>
    )
  }

  if (!attention.owner) {
    return (
      <StarterCard title={t('ownerData')} description={t('ownerDataHint')}>
        <OwnerLoader>
          <OwnerForm />
        </OwnerLoader>
      </StarterCard>
    )
  }

  return (
    <StarterCard title="!Listo!" description="Tu atención ha sido registrada" />
  )
}
