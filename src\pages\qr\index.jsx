import { Spinner } from '@/components/ui/spinner'
import { storage } from '@/services/storage'
import { useEffect } from 'react'
import { useNavigate, useSearchParams } from 'react-router-dom'

function Qr() {
  const navigate = useNavigate()
  const [searchParams] = useSearchParams()
  const slug = searchParams.get('slug')

  useEffect(
    function () {
      if (slug) storage.clinicSlug = slug
      // navigate('/start')
    },
    [navigate, slug],
  )

  return (
    <div className="flex items-center min-h-screen px-4 py-12 sm:px-6 md:px-8 lg:px-12 xl:px-16">
      <div className="w-full space-y-6 text-center">
        <div className="space-y-3">
          <Spinner />
        </div>
      </div>
    </div>
  )
}

export { Qr }
