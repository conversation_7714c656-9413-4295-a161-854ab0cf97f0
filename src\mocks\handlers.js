import { serverlessUrl } from '@/constants'
import { delay, http, HttpResponse } from 'msw'

export const handlers = [
  http.get(`${serverlessUrl}/clinics/:slug`, async ({ params }) => {
    const { slug } = params
    await delay('real')
    return HttpResponse.json({
      id: 1,
      name: 'Veterinaria de prueba',
      slug: slug,
    })
  }),
  http.get(`${serverlessUrl}/clinics/1/reasons`, async () => {
    await delay('real')
    return HttpResponse.json([
      {
        id: 1,
        name: 'Consulta general',
        description: 'Detalles de la consulta general',
      },
      {
        id: 2,
        name: 'Control de vacunación',
        description: 'Detalles del control de vacunación',
      },
      {
        id: 3,
        name: 'Consulta preventiva',
        description: 'Detalles de la consulta preventiva',
      },
      {
        id: 4,
        name: 'Servicio de limpieza',
        description: 'Detalles del servicio de limpieza',
      },
    ])
  }),
  http.get(`${serverlessUrl}/owners`, async ({ request }) => {
    await delay('real')
    const url = new URL(request.url)
    const document = url.searchParams.get('dni')

    if (document !== '12345678') {
      return HttpResponse.json([])
    }

    return HttpResponse.json([
      {
        id: 3,
        name: 'Sofia',
        lastName: 'Davis',
        document: '12345678',
        phone: '987654321',
      },
    ])
  }),
  http.get(`${serverlessUrl}/owners/3/pets`, async () => {
    await delay('real')
    return HttpResponse.json([
      {
        id: 9,
        name: 'Max',
        species: 'Perro',
        breed: 'Labrador',
        gender: 'Macho',
      },
      {
        id: 19,
        name: 'Luna',
        species: 'Gato',
        breed: 'Persa',
        gender: 'Hembra',
      },
    ])
  }),
]
