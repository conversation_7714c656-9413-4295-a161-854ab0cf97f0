import { FormSelect } from '@/components/form-select'
import { Button } from '@/components/ui/button'
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { useTranslation } from '@/i18n'
import { zodResolver } from '@hookform/resolvers/zod'
import { useForm } from 'react-hook-form'
import { z } from 'zod/v4'

const schema = z.object({
  identifier: z.string().length(8).regex(/^\d+$/),
  reason: z.string(),
})

export function StarterForm({ clinicId, onPayload }) {
  const { t } = useTranslation()
  const defaultValues = { identifier: '', reason: '1' }
  const form = useForm({ resolver: zodResolver(schema), defaultValues })

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onPayload)}
        className="flex flex-col gap-6"
      >
        <FormField
          control={form.control}
          name="identifier"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t('identifierLabel')}</FormLabel>
              <FormControl>
                <Input {...field} placeholder={t('identifierPlaceholder')} />
              </FormControl>
              <FormDescription>{t('identifierHint')}</FormDescription>
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="reason"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t('reasonLabel')}</FormLabel>
              <FormSelect
                {...field}
                url={clinicId && `/clinics/${clinicId}/reasons`}
                hint={t('reasonHint')}
              />
            </FormItem>
          )}
        />

        <Button type="submit" className="w-full">
          {t('continue')}
        </Button>
      </form>
    </Form>
  )
}
