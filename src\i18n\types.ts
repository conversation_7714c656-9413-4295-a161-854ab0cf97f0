export type TranslationParams = {
  welcome: { name: string }
  petsCount: { n: number }
  ageYears: { years: number }
  itemsCount: { count: number }
}

export type TranslationKey =
  // Global
  | 'access'
  | 'login'
  | 'logout'
  | 'continue'
  | 'welcome'
  | 'haveAccount'
  | 'petsCount'
  | 'ageYears'
  | 'itemsCount'
  | 'flagName'

  // Starter form
  | 'identifierLabel'
  | 'identifierPlaceholder'
  | 'identifierHint'
  | 'reasonLabel'
  | 'reasonHint'
  | 'ownerData'
  | 'ownerDataHint'
  | 'nameLabel'
  | 'namePlaceholder'
  | 'lastNameLabel'
  | 'lastNamePlaceholder'
  | 'phoneLabel'
  | 'phonePlaceholder'

  // Default values
  | 'noOptions'
  | 'defaultPlaceholder'
  | 'defaultError'
  | 'defaultErrorHint'

type WithParams = keyof TranslationParams
type WithoutParams = Exclude<TranslationKey, WithParams>

export interface TranslationFunction {
  (key: WithoutParams): string

  <KeyWithParams extends WithParams>(
    key: KeyWithParams,
    params: TranslationParams[KeyWithParams],
  ): string
}

export interface TranslationContextType {
  t: TranslationFunction
}
