import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Fallback } from '@/components/ui/fallback'
import { Skeleton } from '@/components/ui/skeleton'
import { useTranslation } from '@/i18n'

export function StarterCard(props) {
  const { children, title, description, onAccess } = props
  const { t } = useTranslation()

  const thereIsNoData = !title || !description

  return (
    <Card>
      <Fallback fallback={<HeaderFallback />} when={thereIsNoData}>
        <CardHeader className="text-center">
          <CardTitle>{title}</CardTitle>
          <CardDescription>{description}</CardDescription>
        </CardHeader>
      </Fallback>

      <CardContent>
        {children}

        <Fallback when={!onAccess} empty>
          <div className="mt-4 text-center text-sm">
            {t('haveAccount')} <span />
            <a
              onClick={onAccess}
              className="underline underline-offset-4 cursor-pointer"
            >
              {t('access')}
            </a>
          </div>
        </Fallback>
      </CardContent>
    </Card>
  )
}

function HeaderFallback() {
  return (
    <CardHeader>
      <CardTitle className="flex justify-center">
        <Skeleton className="h-5 w-1/2" />
      </CardTitle>
      <CardDescription className="flex justify-center">
        <Skeleton className="h-4 w-3/4" />
      </CardDescription>
    </CardHeader>
  )
}
