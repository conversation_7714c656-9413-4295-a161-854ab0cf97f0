import { useTranslation } from '@/i18n'

function Flag({ children, prefix = '+51' }) {
  const { t } = useTranslation()

  return (
    <div className="flex items-center">
      <div className="flex min-w-[4rem] items-center justify-center text-sm">
        <img
          src="https://flagcdn.com/h20/pe.png"
          srcSet="https://flagcdn.com/h40/pe.png 2x"
          alt={t('flagName')}
          className="w-5 h-auto mr-1"
        />
        <span>{prefix}</span>
      </div>
      {children}
    </div>
  )
}

export { Flag }
