import { createContext, useContext } from 'react'

export const Languages = {
  spanish: 'es',
}

export const TranslationContext = createContext()

/**
 * @returns {import('./types').TranslationContextType}
 */
export function useTranslation() {
  const context = useContext(TranslationContext)

  if (!context) {
    throw new Error('useTranslation must be used within an TranslationProvider')
  }

  return context
}

export { TranslationProvider } from './i18n'
