import { lazy } from 'react'

import { Basic } from '@/layouts/basic'
import { Centered } from '@/layouts/centered'

import { Qr } from '@/pages/qr'

/**
 * @type {import('react-router-dom').RouteObject[]}
 */
export const routes = [
  {
    path: '/',
    Component: Basic,
    ErrorBoundary: lazy(() => import('@/pages/error')),
    children: [
      {
        index: true,
        Component: lazy(() => import('@/pages/home')),
      },
      {
        path: '/settings',
        Component: lazy(() => import('@/pages/settings')),
      },
    ],
  },
  {
    path: '/start',
    Component: Centered,
    children: [
      {
        index: true,
        Component: lazy(() => import('@/pages/start')),
      },
    ],
  },
  {
    path: '/qr',
    Component: Qr,
  },
]
