import { FormControl, FormDescription } from '@/components/ui/form'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Skeleton } from '@/components/ui/skeleton'
import { useRequest } from '@/fetcher'
import { useTranslation } from '@/i18n'

export function FormSelect({
  url,
  disabled: externalDisabled = false,
  idKey = 'id',
  displayKey = 'name',
  descriptionKey = 'description',
  placeholder,
  hint,
  value,
  onChange,
  ...props
}) {
  const { t } = useTranslation()
  const { data, isLoading, error } = useRequest(() => url)

  const disabled = externalDisabled || isLoading || !data || error
  const selectedItem = data?.find((item) => item[idKey] == value)
  const description = selectedItem?.[descriptionKey] ?? hint ?? t('noOptions')
  const selectPlaceholder = placeholder ?? t('defaultPlaceholder')

  if (error) {
    return <ErrorFallback />
  }

  if (isLoading || !data) {
    return <LoadingFallback />
  }

  return (
    <Select
      onValueChange={onChange}
      defaultValue={value}
      disabled={disabled}
      {...props}
    >
      <FormControl>
        <SelectTrigger className="w-full">
          <SelectValue placeholder={selectPlaceholder} />
        </SelectTrigger>
      </FormControl>
      <FormDescription>{description}</FormDescription>
      <SelectContent>
        {data?.map((item) => (
          <SelectItem key={item[idKey]} value={`${item[idKey]}`}>
            {item[displayKey]}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  )
}

function ErrorFallback() {
  const { t } = useTranslation()

  return (
    <div className="space-y-2">
      <div className="flex items-center justify-between">
        <div className="h-10 w-full rounded-md border border-destructive bg-destructive/10 flex items-center px-3 text-sm text-destructive cursor-default">
          {t('defaultError')}
        </div>
      </div>
      <p className="text-sm text-destructive">{t('defaultErrorHint')}</p>
    </div>
  )
}

function LoadingFallback() {
  return (
    <div className="space-y-2">
      <div className="flex items-center justify-between">
        <Skeleton className="h-10 w-full rounded-md" />
      </div>
      <Skeleton className="h-4 w-2/3" />
    </div>
  )
}
