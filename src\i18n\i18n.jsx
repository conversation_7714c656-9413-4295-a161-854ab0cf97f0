import { useState } from 'react'
import { Languages, TranslationContext } from '.'

import { es } from './es'

const languages = { es }

export function TranslationProvider({
  children,
  initialLanguage = Languages.spanish,
}) {
  const [language] = useState(initialLanguage)

  const value = {
    t(key, params) {
      const translation = languages[language]?.[key]

      if (!translation) {
        return key
      }

      return parse(translation, params).trim()
    },
  }

  return (
    <TranslationContext.Provider value={value}>
      {children}
    </TranslationContext.Provider>
  )
}

function parse(payload, params = {}) {
  const [key, value] = Object.entries(params).at(0) || []
  const translation = payload.replaceAll(`{${key}}`, value)
  const slices = translation.split('|')

  if (slices.length === 1) {
    return slices.at(0)
  }

  if (value > slices.length - 1) {
    return slices.at(-1)
  }

  return slices.at(value)
}
