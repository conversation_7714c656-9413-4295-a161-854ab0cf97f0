import { Button } from '@/components/ui/button'
import { Flag } from '@/components/ui/flag'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { useTranslation } from '@/i18n'
import { zodResolver } from '@hookform/resolvers/zod'
import { useForm } from 'react-hook-form'
import { toast } from 'sonner'
import { z } from 'zod/v4'

const schema = z.object({
  name: z.string().min(1),
  lastName: z.string().optional(),
  phone: z.string().length(9).regex(/^\d+$/),
})

export function OwnerForm() {
  const { t } = useTranslation()
  const defaultValues = { name: '', lastName: '', phone: '' }
  const form = useForm({ resolver: zodResolver(schema), defaultValues })

  function onSubmit(values) {
    toast(JSON.stringify(values))
  }

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="flex flex-col gap-6"
      >
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t('nameLabel')}</FormLabel>
              <FormControl>
                <Input {...field} placeholder={t('namePlaceholder')} />
              </FormControl>
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="lastName"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t('lastNameLabel')}</FormLabel>
              <FormControl>
                <Input {...field} placeholder={t('lastNamePlaceholder')} />
              </FormControl>
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="phone"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t('phoneLabel')}</FormLabel>
              <FormControl>
                <Flag>
                  <Input {...field} placeholder={t('phonePlaceholder')} />
                </Flag>
              </FormControl>
            </FormItem>
          )}
        />

        <Button type="submit" className="w-full">
          {t('continue')}
        </Button>
      </form>
    </Form>
  )
}
