import { cn } from '@/lib/utils'

function calculate(size) {
  switch (size) {
    case 'small':
      return { height: 4, width: 4, border: 2 }
    case 'medium':
      return { height: 8, width: 8, border: 4 }
    case 'large':
      return { height: 12, width: 12, border: 4 }
  }
}

function Spinner({ size = 'small' }) {
  const { width, height, border } = calculate(size)

  return (
    <div className="flex items-center justify-center">
      <div
        className={cn(
          `h-${height} w-${width} border-${border}`,
          'rounded-full border-t-primary animate-spin',
        )}
      />
    </div>
  )
}

export { Spinner }

