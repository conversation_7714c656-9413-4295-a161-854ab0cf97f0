import { z } from 'zod/v4'

const KEY = 'tempattn'

const TemporalAttention = z.object({
  clinicId: z.coerce.number().nullable().default(null),
  identifier: z.string().length(8).regex(/^\d+$/).nullable().default(null),
  reason: z.coerce.number().nullable().default(null),
  owner: z
    .object({
      id: z.number(),
      name: z.string(),
      lastName: z.string().optional(),
      phone: z.string().length(9).regex(/^\d+$/),
    })
    .nullable()
    .default(null),
  pet: z.coerce.number().nullable().default(null),
})

export function getTemporalAttention() {
  const item = localStorage.getItem(KEY)
  const object = JSON.parse(item || '{}')
  return TemporalAttention.parse(object)
}

export function addTo(payload) {
  const draft = getTemporalAttention()
  const parsed = TemporalAttention.parse({ ...draft, ...payload })
  const value = JSON.stringify(parsed)
  localStorage.setItem(KEY, value)
}
