export class BrowserStorage {
  constructor() {
    this.schemas = {}
  }

  registerKey(propertyName, storageKey, schema) {
    if (this[propertyName]) {
      throw new Error(`The property "${propertyName}" is already defined.`)
    }

    this.schemas[propertyName] = { key: storageKey, schema }

    Object.defineProperty(this, propertyName, {
      get() {
        return this.getItem(propertyName)
      },
      set(value) {
        this.setItem(propertyName, value)
      },
      enumerable: true,
      configurable: false,
    })
  }

  getItem(propertyName) {
    const { key, schema } = this.schemas[propertyName]
    const raw = localStorage.getItem(key)

    try {
      const parsed = JSON.parse(raw)
      const result = schema.safeParse(parsed ?? {})
      return result.success ? result.data : null
    } catch {
      return null
    }
  }

  setItem(propertyName, payload) {
    const { key, schema } = this.schemas[propertyName]
    const currentValue = this.getItem(propertyName)
    const draft = take(currentValue, payload)
    const result = schema.safeParse(draft)

    if (!result.success) {
      console.warn(`Invalid value for "${propertyName}":`, result.error)
      return
    }

    localStorage.setItem(key, JSON.stringify(result.data))
  }

  clear(propertyName) {
    const { key } = this.schemas[propertyName] || {}
    if (key) localStorage.removeItem(key)
  }

  clearAll() {
    Object.values(this.schemas).forEach(({ key }) =>
      localStorage.removeItem(key),
    )
  }
}

function take(currentValue, payload) {
  if (typeof payload === 'object') {
    return { ...currentValue, ...payload }
  }

  return payload
}
